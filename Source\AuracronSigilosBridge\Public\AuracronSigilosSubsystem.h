// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sigils Subsystem for managing active sigils per player
// UE 5.6 Compatible Implementation

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "GameplayTagContainer.h"
#include "Engine/World.h"
#include "GameFramework/Pawn.h"
#include "AuracronSigilosBridge.h"
#include "AuracronSigilosSubsystem.generated.h"

/**
 * Structure to track active sigils for a player
 */
USTRUCT()
struct AURACRONSIGILOSBRIDGE_API FPlayerSigilData
{
    GENERATED_BODY()

    /** Active sigils for this player */
    UPROPERTY()
    TArray<EAuracronSigiloType> ActiveSigils;

    /** Sigil activation timestamps (for duration tracking) */
    UPROPERTY()
    TMap<EAuracronSigiloType, float> SigilTimestamps;

    /** Sigil durations (0 = permanent) */
    UPROPERTY()
    TMap<EAuracronSigiloType, float> SigilDurations;

    FPlayerSigilData()
    {
        ActiveSigils.Empty();
        SigilTimestamps.Empty();
        SigilDurations.Empty();
    }
};

/**
 * UAuracronSigilosSubsystem
 * 
 * World subsystem responsible for managing active sigils for all players
 * Integrates with UE 5.6 Gameplay Ability System
 * Provides centralized sigil state management across the game world
 */
UCLASS(BlueprintType)
class AURACRONSIGILOSBRIDGE_API UAuracronSigilosSubsystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    UAuracronSigilosSubsystem();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // UWorldSubsystem interface
    virtual void OnWorldBeginPlay(UWorld& InWorld) override;
    virtual bool DoesSupportWorldType(const EWorldType::Type WorldType) const override;

    // === Core Sigil Management ===

    /**
     * Get all active sigils for a specific player
     * @param Player The player pawn to check
     * @return Array of active sigil types
     */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Sigils")
    TArray<EAuracronSigiloType> GetActiveSigils(APawn* Player) const;

    /**
     * Activate a sigil for a player
     * @param Player The player pawn
     * @param SigilType The sigil type to activate
     * @param Duration Duration in seconds (0 = permanent)
     * @return True if successfully activated
     */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Sigils")
    bool ActivateSigil(APawn* Player, EAuracronSigiloType SigilType, float Duration = 0.0f);

    /**
     * Deactivate a sigil for a player
     * @param Player The player pawn
     * @param SigilType The sigil type to deactivate
     * @return True if successfully deactivated
     */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Sigils")
    bool DeactivateSigil(APawn* Player, EAuracronSigiloType SigilType);

    /**
     * Check if a player has a specific sigil active
     * @param Player The player pawn
     * @param SigilType The sigil type to check
     * @return True if the sigil is active
     */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron|Sigils")
    bool HasActiveSigil(APawn* Player, EAuracronSigiloType SigilType) const;

    /**
     * Get the number of active sigils for a player
     * @param Player The player pawn
     * @return Number of active sigils
     */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron|Sigils")
    int32 GetActiveSigilCount(APawn* Player) const;

    /**
     * Clear all active sigils for a player
     * @param Player The player pawn
     */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Sigils")
    void ClearAllSigils(APawn* Player);

    // === Events ===

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilActivated, APawn*, Player, EAuracronSigiloType, SigilType, float, Duration);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilDeactivated, APawn*, Player, EAuracronSigiloType, SigilType);

    /** Event fired when a sigil is activated */
    UPROPERTY(BlueprintAssignable, Category = "Auracron|Sigils|Events")
    FOnSigilActivated OnSigilActivated;

    /** Event fired when a sigil is deactivated */
    UPROPERTY(BlueprintAssignable, Category = "Auracron|Sigils|Events")
    FOnSigilDeactivated OnSigilDeactivated;

protected:
    /** Map of player sigil data */
    UPROPERTY()
    TMap<TWeakObjectPtr<APawn>, FPlayerSigilData> PlayerSigilMap;

    /** Timer handle for sigil duration management */
    FTimerHandle SigilTimerHandle;

    /**
     * Update sigil durations and remove expired sigils
     */
    void UpdateSigilDurations();

    /**
     * Get or create player sigil data
     * @param Player The player pawn
     * @return Reference to player sigil data
     */
    FPlayerSigilData& GetOrCreatePlayerSigilData(APawn* Player);

    /**
     * Clean up invalid player references
     */
    void CleanupInvalidPlayers();
};