# -*- coding: utf-8 -*-
"""
Planície Radiante - Arquivo de Configuração
Configuração centralizada para geração procedural de terreno AAA
Compatível com Unreal Engine 5.6
"""

# =============================================================================
# CONFIGURAÇÕES DE TERRENO
# =============================================================================

TERRAIN_CONFIG = {
    # Dimensões do terreno
    'size_x': 8000,  # metros (8km)
    'size_y': 8000,  # metros (8km)
    'heightmap_resolution': 2017,  # pixels (potência de 2 + 1)
    
    # Transformação do landscape
    'scale': (100.0, 100.0, 100.0),  # escala XYZ
    'location': (0.0, 0.0, 0.0),  # posição no mundo
    'rotation': (0.0, 0.0, 0.0),  # rotação
    
    # Parâmetros de geração procedural
    'noise_scale': 0.01,  # escala do ruído Perlin
    'amplitude': 500.0,  # amplitude das elevações (metros)
    'octaves': 4,  # número de camadas de ruído
    'persistence': 0.5,  # persistência entre octaves
    'lacunarity': 2.0,  # lacunaridade (frequência)
    
    # Limites de elevação
    'min_elevation': -100.0,  # metros
    'max_elevation': 400.0,   # metros
    
    # Configurações de qualidade
    'material_path': '/Game/Materials/Terrain/M_PlanicieRadiante',
    'texture_resolution': 1024,  # resolução das texturas
    'lod_levels': 4,  # níveis de LOD
}

# =============================================================================
# CONFIGURAÇÕES AAA DE PERFORMANCE
# =============================================================================

PERFORMANCE_CONFIG = {
    # Targets de performance AAA
    'target_fps': 60,  # FPS mínimo
    'max_memory_gb': 4,  # GB máximo de memória
    'max_load_time_seconds': 30,  # segundos máximo de carregamento
    
    # Limites de otimização
    'max_components': 10000,  # componentes máximos
    'max_heightmap_resolution': 4096,  # resolução máxima do heightmap
    'max_landscape_scale': 200.0,  # escala máxima do landscape
    
    # Configurações de streaming
    'enable_world_partition': True,
    'streaming_distance': 10000,  # metros
    'unload_distance': 15000,  # metros
    
    # Configurações de LOD
    'lod_distance_factor': 1.0,
    'force_lod_level': -1,  # -1 = automático
}

# =============================================================================
# CONFIGURAÇÕES DE WORLD PARTITION
# =============================================================================

WORLD_PARTITION_CONFIG = {
    'enable_streaming': True,
    'grid_size': 1600,  # metros por célula
    'loading_range': 3200,  # metros
    'enable_hlods': True,
    'hlod_layer_count': 3,
}

# =============================================================================
# CONFIGURAÇÕES DE DATA LAYERS
# =============================================================================

DATA_LAYER_CONFIG = {
    'layer_name': 'PlanicieRadiante_Terrain',
    'layer_label': 'Planície Radiante - Terreno',
    'is_runtime_loaded': True,
    'is_initially_loaded': True,
    'is_initially_visible': True,
    'debug_color': (0.2, 0.8, 0.3, 1.0),  # Verde radiante
}

# =============================================================================
# CONFIGURAÇÕES DO DYNAMIC REALM SYSTEM
# =============================================================================

DYNAMIC_REALM_CONFIG = {
    'target_layer_name': 'Terrestrial',
    'layer_index': 0,
    'auto_activate_layer': True,
    'register_for_evolution': True,
    'realm_priority': 1,
}

# =============================================================================
# CONFIGURAÇÕES DE TESTES
# =============================================================================

TEST_CONFIG = {
    'run_performance_tests': True,
    'run_integration_tests': True,
    'run_quality_tests': True,
    'minimum_pass_rate': 0.8,  # 80% dos testes devem passar
    'enable_detailed_logging': True,
    'save_test_results': True,
    'test_results_path': 'C:/Aura/projeto/Auracron/Logs/terrain_tests.json',
}

# =============================================================================
# CONFIGURAÇÕES DE LOGGING
# =============================================================================

LOGGING_CONFIG = {
    'enable_console_output': True,
    'enable_file_logging': True,
    'log_file_path': 'C:/Aura/projeto/Auracron/Logs/planicie_radiante.log',
    'log_level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'max_log_file_size_mb': 10,
    'backup_count': 5,
}

# =============================================================================
# CONFIGURAÇÕES DE BIOMA
# =============================================================================

BIOME_CONFIG = {
    'biome_type': 'radiante',
    'ambient_light_color': (1.0, 0.9, 0.7, 1.0),  # Luz dourada
    'ambient_light_intensity': 1.2,
    'fog_color': (0.8, 0.9, 1.0, 1.0),  # Névoa azulada
    'fog_density': 0.02,
    'sky_sphere_material': '/Game/Materials/Sky/M_SkyRadiante',
}

# =============================================================================
# VALIDAÇÃO DE CONFIGURAÇÕES
# =============================================================================

def validate_config():
    """
    Valida se todas as configurações estão dentro dos parâmetros AAA
    """
    errors = []
    
    # Validar dimensões do terreno
    if TERRAIN_CONFIG['size_x'] > 16000 or TERRAIN_CONFIG['size_y'] > 16000:
        errors.append("Dimensões do terreno excedem limite AAA (16km)")
    
    # Validar resolução do heightmap
    if TERRAIN_CONFIG['heightmap_resolution'] > PERFORMANCE_CONFIG['max_heightmap_resolution']:
        errors.append("Resolução do heightmap excede limite de performance")
    
    # Validar targets de performance
    if PERFORMANCE_CONFIG['target_fps'] < 30:
        errors.append("Target de FPS abaixo do mínimo AAA (30 FPS)")
    
    # Validar configurações de World Partition
    if WORLD_PARTITION_CONFIG['grid_size'] < 800:
        errors.append("Grid size muito pequeno para otimização adequada")
    
    if errors:
        print("[ERROR] Problemas de configuração encontrados:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("[SUCCESS] Todas as configurações validadas com sucesso")
    return True

# =============================================================================
# CONFIGURAÇÕES AVANÇADAS (OPCIONAL)
# =============================================================================

ADVANCED_CONFIG = {
    # Configurações experimentais
    'enable_nanite': False,  # Nanite para geometria complexa
    'enable_lumen': True,   # Lumen para iluminação global
    'enable_chaos_physics': True,  # Sistema de física Chaos
    
    # Configurações de desenvolvimento
    'debug_mode': False,
    'show_debug_info': False,
    'enable_profiling': False,
    'auto_save_after_generation': True,
}

# Executar validação ao importar
if __name__ == "__main__":
    validate_config()